#!/usr/bin/env bash

COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2
SHA1=$3

# 排除合并等特殊情况
[ "$COMMIT_SOURCE" = "merge" ] && exit 0
[ "$COMMIT_SOURCE" = "commit" ] && [ -n "$SHA1" ] && exit 0

# 避免影响git rebase以及vscode的提交信息编辑
[ "$COMMIT_SOURCE" = "message" ] && exit 0

# 通用模板框架
TEMPLATE="# 请选择提交类型并删除其他不需要的部分
[module] Fix Bug {BUG_ID}: {Description}

[模块] 中文标题

# ================ Commit Type ================
# Bugfix: [Module][bug] Fix Bug BUG_ID: Description (max 65 chars/line)
#     Must include: \"Issue Description\" \"Root Cause\" \"Solution\" \"Test Scope\"
# Device Directory: [Device Model] Description
#     Must include: \"Test Scope\"
# Feature: [Module] Description
#     Must include: \"Test Scope\"
# 修复bug的提交：[模块名][bug] Fix Bug BUG_ID: 描述，单行最多不超过 65 字符
#     必须包含以下标签：\"问题描述\" \"问题原因\" \"解决方案\" \"自测内容\"
# 机型目录的提交：[机型名] 描述
#     必须包含以下标签：\"自测内容\"
# 添加功能的提交：[模块名] 描述
#     必须包含以下标签：\"自测内容\"
# ------------------- Tags ---------------------
[Issue Description]
# Describe the problem (max 70 chars/line)
[Root Cause] 
# Explain the root cause
[Solution] 
# Describe the fix
[Requirement]
# For feature commits
# Ref: TLXXXX
[Model Adaptation]
# For device-specific fixes
# Requires Config_package_amazon_ffs in iplatform.config, add <aaaa><aaaa> in profile
[Test Scope] 
# List test cases
[问题描述]
# 请具体描述问题现象，每行最多不超过70字符
[问题原因] 
# 请说明问题根源
[解决方案] 
# 请描述修复方案
[需求]
# 用于添加功能的提交
# 见联络单 TLXXXX
[机型适配]
# 用于添加功能的提交
# iplatform.config中需开启Config_package_amazon_ffs的宏，profile中需添加<aaaa><aaaa>
[自测内容] 
# 请列出测试要点

"
echo "正在添加模板..." >> /tmp/git-hook.log
echo "file: $COMMIT_MSG_FILE" >> /tmp/git-hook.log
echo "source: $COMMIT_SOURCE" >> /tmp/git-hook.log
echo "sha1: $SHA1" >> /tmp/git-hook.log

# 保存现有内容并添加模板
if [ -f "$COMMIT_MSG_FILE.bak" ]; then

    cat "$COMMIT_MSG_FILE" > "$COMMIT_MSG_FILE.current"
    # 使用grep过滤注释行，但保留以"# :["开头的行
    grep -v '^#\( :\[\)\@!' "$COMMIT_MSG_FILE.bak" > "$COMMIT_MSG_FILE"

    # 模板转换为注释后添加到末尾
    echo "$TEMPLATE" | sed -e '/^#/d;s/^/# /' >> "$COMMIT_MSG_FILE"
    cat "$COMMIT_MSG_FILE.current" >> "$COMMIT_MSG_FILE"
else
    # 如果文件不存在，直接写入模板
    cat "$COMMIT_MSG_FILE" > "$COMMIT_MSG_FILE.current"
    echo -e "$TEMPLATE" > "$COMMIT_MSG_FILE"
    cat "$COMMIT_MSG_FILE.current" >> "$COMMIT_MSG_FILE"
fi

exit 0
