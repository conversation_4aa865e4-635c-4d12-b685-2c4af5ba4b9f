# Git状态测试功能说明

## 概述
增强的测试工具现在支持模拟不同的git操作状态，可以测试commit-msg hook在各种git环境下的行为。

## 支持的Git状态

### 1. cherry-pick状态
- **文件命名**: `XXX-description.cherry-pick.accept/deny.txt`
- **模拟方式**: 创建 `.git/CHERRY_PICK_HEAD` 文件
- **预期行为**: 当前实现会跳过commit-msg验证

### 2. rebase状态  
- **文件命名**: `XXX-description.rebase.accept/deny.txt`
- **模拟方式**: 创建 `.git/rebase-merge/` 目录和相关文件
- **预期行为**: 仍然执行正常的commit-msg验证

### 3. merge状态
- **文件命名**: `XXX-description.merge.accept/deny.txt`
- **模拟方式**: 创建 `.git/MERGE_HEAD` 文件
- **预期行为**: 仍然执行正常的commit-msg验证

### 4. revert状态
- **文件命名**: `XXX-description.revert.accept/deny.txt`
- **模拟方式**: 创建 `.git/REVERT_HEAD` 文件
- **预期行为**: 仍然执行正常的commit-msg验证

### 5. bisect状态
- **文件命名**: `XXX-description.bisect.accept/deny.txt`
- **模拟方式**: 创建 `.git/BISECT_LOG` 文件
- **预期行为**: 仍然执行正常的commit-msg验证

### 6. worktree状态
- **文件命名**: `XXX-description.worktree.accept/deny.txt`
- **模拟方式**: 创建 `.git/commondir` 文件模拟worktree环境
- **预期行为**: 仍然执行正常的commit-msg验证

## 测试用例示例

### Cherry-pick状态测试（跳过验证）
```
文件名: 025-cherry-pick-skip-validation.cherry-pick.accept.txt
内容: 无效的提交消息（缺少模块标签等），但在cherry-pick状态下应该通过
```

### Rebase状态测试（正常验证）
```
文件名: 028-rebase-test.rebase.accept.txt
内容: [module] 有效的提交消息，包含所有必需标签
```

### 状态测试失败用例
```
文件名: 031-rebase-invalid.rebase.deny.txt
内容: 无效的提交消息，即使在rebase状态下也应该被拒绝
```

## 运行测试
```bash
cd teams/Archer/tests
./run_tests.sh
```

测试输出会显示每个测试的git状态：
```
[cherry-pick] ✓ PASS: 025-cherry-pick-skip-validation.cherry-pick.accept
[rebase] ✓ PASS: 028-rebase-test.rebase.accept
[merge] ✓ PASS: 029-merge-test.merge.accept
[worktree] ✓ PASS: 030-worktree-test.worktree.accept
```

## 添加新的Git状态测试

1. 创建测试文件，使用命名格式：`XXX-description.state.accept/deny.txt`
2. 如需支持新的git状态，在 `run_tests.sh` 的 `setup_git_state()` 函数中添加相应的模拟逻辑
3. 运行测试验证行为是否符合预期

## 注意事项

- 每个测试运行前会设置对应的git状态，测试完成后会清理状态文件
- 测试在临时git仓库中运行，不会影响实际的工作目录
- 状态模拟通过创建/删除特定的git内部文件实现，与真实的git操作状态一致
