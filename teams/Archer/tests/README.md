# Archer团队 commit-msg Hook 测试套件

## 概述
本目录包含了Archer团队commit-msg钩子的自动化测试用例，用于验证提交消息格式的正确性。

## 测试规则
当前commit-msg钩子验证以下规则：

1. **所有提交都必须包含 `[自测内容]` 标签**
2. **Fix类型提交还需要包含以下标签：**
   - `[问题描述]`：具体描述问题现象
   - `[问题原因]`：说明问题根源
   - `[解决方案]`：描述修复方案
   - `[自测内容]`：列出测试要点

3. **Bug提交验证规则：**
   - 标题包含"Bug"或"bug"时，必须包含"Bug 12345"格式的ID（5-11位数字）
   - 必须包含 `[bug]` 标签

4. **Head格式要求：**
   - 第一行必须以 `[模块名]` 开头，后跟空格
   - 支持多模块格式：`[模块1][模块2] 描述`
   - 第一行长度不超过65字符
   - 第二、三行不能包含模块名标签
   - **标题首行不能包含中文字符**

5. **Body格式要求：**
   - Head与Body之间必须有空行
   - Body每行长度不超过70字符（支持中文字符宽度计算）
   - **正文至少需要包含一行不包含中文字符的行**

## 测试文件命名规范
- `XXX-描述.accept.txt`：期望通过验证的测试用例
- `XXX-描述.deny.txt`：期望被拒绝的测试用例
- `XXX-描述.状态.accept.txt`：在特定git状态下期望通过验证的测试用例
- `XXX-描述.状态.deny.txt`：在特定git状态下期望被拒绝的测试用例
- `XXX`为三位数字编号，按顺序递增

### 支持的Git状态
测试工具现在支持模拟不同的git操作状态：
- `cherry-pick`：模拟cherry-pick操作状态
- `rebase`：模拟rebase操作状态
- `merge`：模拟merge操作状态
- `revert`：模拟revert操作状态
- `bisect`：模拟bisect操作状态
- `worktree`：模拟worktree环境

例如：`025-cherry-pick-test.cherry-pick.accept.txt` 会在cherry-pick状态下测试提交消息验证

## 运行测试
```bash
./run_tests.sh
```

## Git状态测试
测试工具现在支持模拟不同的git操作状态。详细说明请参考 [GIT_STATE_TESTING.md](./GIT_STATE_TESTING.md)。

## 添加新测试用例
1. 在tests目录下创建新的测试文件，遵循命名规范
2. 文件内容为完整的commit消息内容
3. 运行测试验证新用例是否按预期工作

## 当前测试用例说明
- `001-valid-fix-commit.accept.txt`：标准Fix提交格式
- `002-valid-feature-commit.accept.txt`：功能提交格式
- `003-invalid-no-module.deny.txt`：缺少模块名标签
- `004-invalid-head-too-long.deny.txt`：Head第一行过长
- `005-invalid-no-blank-line.deny.txt`：Head与Body间缺少空行
- `006-invalid-body-line-too-long.deny.txt`：Body行过长
- `007-invalid-fix-missing-tags.deny.txt`：Fix提交缺少必要标签
- `008-valid-chinese-characters.accept.txt`：包含中文字符的提交
- `009-valid-multiple-modules.accept.txt`：多模块标签格式
- `010-valid-fix-with-all-tags.accept.txt`：完整Fix提交格式
- `011-multiple-headline.accept.txt`：多行Head格式（有效）
- `011-multiple-headline.deny.txt`：多行Head格式（无效）
- `012-invalid-feature-missing-test-tag.deny.txt`：功能提交缺少自测内容标签
- `013-valid-feature-with-test-tag.accept.txt`：包含自测内容标签的功能提交
- `014-valid-bug-commit.accept.txt`：有效的Bug提交格式
- `015-invalid-bug-no-id.deny.txt`：Bug提交缺少ID
- `016-invalid-bug-no-tag.deny.txt`：Bug提交缺少[bug]标签
- `017-invalid-bug-short-id.deny.txt`：Bug ID位数不足
- `018-invalid-bug-with-text.deny.txt`：Bug ID格式错误
- `019-valid-lowercase-bug.accept.txt`：小写bug的有效提交
- `020-invalid-chinese-in-title.deny.txt`：标题包含中文字符（拒绝）
- `021-invalid-no-non-chinese-line.deny.txt`：正文没有非中文行（拒绝）
- `022-valid-new-rules.accept.txt`：符合新规则的有效提交
- `023-invalid-chinese-title-valid-body.deny.txt`：标题有中文但正文符合要求（拒绝）
- `024-invalid-valid-title-chinese-body.deny.txt`：标题符合要求但正文全是中文（拒绝）
